.loader {
  border: 3px solid #f3f3f3;
  /* Light grey */
  border-top: 3px solid #3498db;
  /* Blue */
  border-radius: 50%;
  position: absolute;
  width: 24px;
  height: 24px;
  top: 50%;
  left: 50%;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Development mode toggle highlighting */
.ai-actions-setting-item-highlighted {
  background-color: var(--background-modifier-hover);
  border: 1px solid var(--background-modifier-border);
  border-radius: 6px;
  padding: 8px;
  margin-bottom: 12px;
}

.ai-actions-dev-options-container {
  margin-left: 16px;
  padding-left: 16px;
  border-left: 2px solid var(--background-modifier-border);
}

.ai-actions-modal-title {
  font-size: 1.3em;
  font-weight: normal;
}

/* Button container for responsive layout */
.ai-actions-button-container {
  display: flex;
  flex-direction: column;
  gap: 0;
  margin-top: 16px;
}

/* Remove default setting styling */
.ai-actions-primary-buttons,
.ai-actions-secondary-buttons {
  margin-bottom: 0 !important;
  border: none !important;
  padding: 0 !important;
}

.ai-actions-primary-buttons .setting-item,
.ai-actions-secondary-buttons .setting-item {
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

.ai-actions-primary-buttons .setting-item-info,
.ai-actions-secondary-buttons .setting-item-info {
  display: none !important;
}

.ai-actions-primary-buttons .setting-item-control,
.ai-actions-secondary-buttons .setting-item-control {
  display: flex;
  flex-wrap: nowrap;
  gap: 8px;
  justify-content: center;
  margin: 0 !important;
  padding: 8px 0;
}

.ai-actions-primary-buttons .setting-item-control button,
.ai-actions-secondary-buttons .setting-item-control button {
  flex: 1;
  min-width: 70px;
  white-space: nowrap;
}

/* Desktop layout - all buttons in one row */
@media (min-width: 768px) {
  .ai-actions-button-container {
    flex-direction: row;
    gap: 16px;
  }
  
  .ai-actions-primary-buttons,
  .ai-actions-secondary-buttons {
    flex: 1;
  }
  
  .ai-actions-primary-buttons .setting-item-control {
    justify-content: flex-start;
  }
  
  .ai-actions-secondary-buttons .setting-item-control {
    justify-content: flex-end;
  }
  
  .ai-actions-primary-buttons .setting-item-control button,
  .ai-actions-secondary-buttons .setting-item-control button {
    flex: 0 0 auto;
    min-width: 80px;
  }
}

/* Mobile layout - buttons in two rows */
@media (max-width: 767px) {
  .ai-actions-primary-buttons .setting-item-control button,
  .ai-actions-secondary-buttons .setting-item-control button {
    font-size: 0.9em;
    padding: 6px 8px;
    min-width: 60px;
  }
}