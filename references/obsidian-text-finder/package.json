{"name": "obsidian-text-finder", "version": "0.2.2", "description": "Provides a find/replace window in edit mode similar to VSCode (supports regular expressions and case sensitivity).", "main": "main.js", "scripts": {"dev": "node esbuild.config.mjs", "build": "tsc -noEmit -skipLibCheck && node esbuild.config.mjs production", "version": "node version-bump.mjs && git add manifest.json versions.json"}, "keywords": ["obsidian", "text", "find", "replace", "regular express"], "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "devDependencies": {"@tsconfig/svelte": "5.0.4", "@types/node": "^16.11.6", "@typescript-eslint/eslint-plugin": "5.29.0", "@typescript-eslint/parser": "5.29.0", "builtin-modules": "3.3.0", "esbuild": "0.25.0", "esbuild-sass-plugin": "^3.3.1", "esbuild-svelte": "0.8.2", "obsidian": "latest", "postcss": "8.4.47", "sass": "^1.79.4", "svelte": "4.2.19", "svelte-preprocess": "6.0.3", "tslib": "2.4.0", "typescript": "5.6.2"}, "dependencies": {"@codemirror/language": "6.10.3", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.23.0", "i18next": "23.15.1", "lucide-svelte": "^0.446.0"}}