{"plugin": {"name": "Text Finder"}, "search": {"tip": {"NoResults": "无结果", "HasResults": "第{{current}}项，共{{total}}项", "ToggleReplace": "切换替换", "MatchCase": "区分大小写", "UseRegularExpression": "使用正则表达式", "PreviousMatch": "上一个匹配项", "NextMatch": "下一个匹配项", "Close": "关闭 (Escape)", "Replace": "替换", "ReplaceAll": "全部替换", "FindPlaceholder": "查找", "ReplacePlaceholder": "替换"}}, "commands": {"ShowFind": {"name": "在当前文件中：打开查找"}, "ShowFindAndReplace": {"name": "在当前文件中：打开查找与替换"}, "HideFind": {"name": "关闭查找窗口"}, "ToggleReplace": {"name": "切换替换窗口"}, "ToggleFind": {"name": "切换查找窗口"}, "PreviousMatch": {"name": "上一个匹配"}, "NextMatch": {"name": "下一个匹配"}, "Replace": {"name": "在当前文件中：替换"}, "ReplaceAll": {"name": "在当前文件中：替换所有"}}, "settings": {"ClearAfterHidden": {"name": "清空搜索文本", "desc": "在关闭搜索窗口后清空搜索文本"}, "EnableInputHotkeys": {"name": "启用输入框的快捷键", "desc": "为输入框添加快捷键（Enter）。搜索：到下一个匹配项，替换：替换一次"}, "SourceModeWhenSearch": {"name": "搜索时强制源代码模式", "desc": "在搜索过程中强制使用源代码模式（某些搜索结果无法直接显示在实时预览中，如图像和超链接）"}, "MoveCursorToMatch": {"name": "将光标移动至匹配项", "desc": "搜索时移动光标并选择当前匹配项"}, "UseSelectionAsSearch": {"name": "将所选内容用作搜索文本", "desc": "将所选文本用作搜索文本"}, "UseObsidianSearchInRead": {"name": "阅读模式下使用Obsidian搜索", "desc": "在阅读模式下，使用命令 \"{{name}}\" 将会调用Obsidian原生的搜索功能(插件提供的搜索与替换仅适用于编辑模式)"}, "UseEscapeCharInReplace": {"name": "支持替换输入框中使用转义字符", "desc": "在正则表达式模式下，替换输入框中可以使用换行(LF)和水平制表符(HT)的转义字符"}}}