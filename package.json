{"name": "obsidian-ai-actions", "version": "0.1.0", "description": "Obsidian AI Actions - Based on AI-powered Text Editor by <PERSON><PERSON><PERSON>", "main": "main.js", "scripts": {"dev": "node esbuild.config.mjs", "build": "tsc -noEmit -skipLibCheck && node esbuild.config.mjs production", "version": "node version-bump.mjs && npm i --package-lock-only && git add manifest.json versions.json package.json package-lock.json"}, "keywords": ["llm", "obsidian", "ai"], "author": "<PERSON>", "license": "MIT", "devDependencies": {"@types/node": "^16.11.6", "@typescript-eslint/eslint-plugin": "5.29.0", "@typescript-eslint/parser": "5.29.0", "builtin-modules": "3.3.0", "esbuild": "0.17.3", "obsidian": "latest", "tslib": "2.4.0", "typescript": "4.7.4"}, "dependencies": {"langchain": "^0.0.156", "openai": "^4.22.0"}}